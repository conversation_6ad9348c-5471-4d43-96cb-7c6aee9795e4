const http = require('http');
const httpProxy = require('http-proxy-middleware');
const express = require('express');
const path = require('path');

const app = express();

// 静态文件服务
app.use(express.static('.'));

// 代理配置
const apiProxy = httpProxy.createProxyMiddleware('/api', {
  target: 'http://localhost:8080',
  changeOrigin: true,
  pathRewrite: {
    '^/api': '/services/whiskerguardorgservice/api'
  },
  onError: (err, req, res) => {
    console.error('代理错误:', err.message);
    res.status(500).json({
      title: '后端服务连接失败',
      message: '请确认后端服务是否正常运行'
    });
  }
});

app.use('/api', apiProxy);

// 启动服务器
const PORT = 3001;
app.listen(PORT, () => {
  console.log(`代理服务器运行在 http://localhost:${PORT}`);
  console.log(`请访问: http://localhost:${PORT}/book-form.html`);
});
