<!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>申请体验 - 猫伯伯智能合规管家</title>
<link rel="stylesheet" href="img/book.css">
</head>

<body>
<!-- 背景效果 -->
<div class="page-background"></div>
<div class="grid-bg"></div>
<div class="particles" id="particles"></div>

<!-- 浮动装饰 -->
<div class="floating-shapes">
  <div class="shape shape-1"></div>
  <div class="shape shape-2"></div>
</div>

<!-- 导航栏 -->
<nav class="navbar">
  <div class="nav-container"> <a href="index.html" class="logo">
    <div class="logo-icon"><img src="img/logo-white.png" alt="猫伯伯智能合规管家"></div>
    猫伯伯 </a> <a href="index.html" class="back-btn"> <span>←</span> 返回首页 </a> </div>
</nav>

<!-- 主内容 -->
<div class="booking-container"> 
  <!-- 左侧信息区 -->
  <div class="info-section">
    <div class="info-badge">开启7天免费体验</div>
    <h1 class="info-title">让合规管理<br>
      变得前所未有的简单</h1>
    <p class="info-desc"> 只需填写简单信息，我们将为您定制专属解决方案。 </p>
    <ul class="benefits-list">
      <li class="benefit-item">
        <div class="benefit-icon">✨</div>
        <span>7天免费试用，无需付费</span> </li>
      <li class="benefit-item">
        <div class="benefit-icon">🛡️</div>
        <span>专业团队1对1实施服务</span> </li>
<!--
      <li class="benefit-item">
        <div class="benefit-icon">⚡</div>
        <span>最快3天完成部署上线</span> </li>
-->
      <li class="benefit-item">
        <div class="benefit-icon">📊</div>
        <span>定制化合规管理方案</span> </li>
    </ul>
    <div class="trust-badges">
      <div class="trust-item"> <span class="trust-number">5年+</span> <span class="trust-label">专业合规经验</span> </div>
      <div class="trust-item"> <span class="trust-number">3倍</span> <span class="trust-label">效率提升目标</span> </div>
      <div class="trust-item"> <span class="trust-number">70%</span> <span class="trust-label">成本节约空间</span> </div>
		
		
    </div>
  </div>
  
  <!-- 右侧表单区 -->
  <div class="form-section">
    <div class="form-header">
      <h2 class="form-title">预约免费体验</h2>
      <p class="form-subtitle">请填写您的信息，我们将尽快与您联系</p>
    </div>
    <form id="bookingForm">
      <div class="form-row">
        <div class="form-group">
          <label class="form-label">您的姓名 <span>*</span></label>
          <input type="text" name="name" class="form-input" placeholder="请输入您的姓名" required>
        </div>
        <div class="form-group">
          <label class="form-label">职位 <span>*</span></label>
          <input type="text" name="position" class="form-input" placeholder="如：合规总监" required>
        </div>
      </div>
      <div class="form-row">
        <div class="form-group">
          <label class="form-label">手机号码 <span>*</span></label>
          <input type="tel" name="mobile" class="form-input" placeholder="请输入手机号码" required>
        </div>
        <div class="form-group">
          <label class="form-label">电子邮箱 <span>*</span></label>
          <input type="email" name="email" class="form-input" placeholder="<EMAIL>" required>
        </div>
      </div>
      <div class="form-group">
        <label class="form-label">公司名称 <span>*</span></label>
        <input type="text" name="company" class="form-input" placeholder="请输入公司全称" required>
      </div>
      <div class="form-row">
        <div class="form-group">
          <label class="form-label">所属行业 <span>*</span></label>
          <select name="industry" class="form-select" required>
            <option value="">请选择行业</option>
            <option value="制造业">制造业</option>
            <option value="金融业">金融业</option>
            <option value="能源电力">能源电力</option>
            <option value="医药健康">医药健康</option>
            <option value="互联网科技">互联网科技</option>
            <option value="房地产">房地产</option>
            <option value="零售贸易">零售贸易</option>
            <option value="其他">其他</option>
          </select>
        </div>
        <div class="form-group">
          <label class="form-label">企业规模</label>
          <select name="companySize" class="form-select">
            <option value="">请选择规模</option>
            <option value="1-50人">1-50人</option>
            <option value="51-200人">51-200人</option>
            <option value="201-500人">201-500人</option>
            <option value="501-1000人">501-1000人</option>
            <option value="1000人以上">1000人以上</option>
          </select>
        </div>
      </div>
      <div class="form-group">
        <label class="form-label">您最关注的合规管理需求</label>
        <div class="radio-group">
          <label class="radio-label">
            <input type="radio" name="focusNeed" value="制度管理" class="radio-input">
            制度管理 </label>
          <label class="radio-label">
            <input type="radio" name="focusNeed" value="风险识别" class="radio-input">
            风险识别 </label>
          <label class="radio-label">
            <input type="radio" name="focusNeed" value="合同审查" class="radio-input">
            合同审查 </label>
          <label class="radio-label">
            <input type="radio" name="focusNeed" value="全面解决方案" class="radio-input" checked>
            全面解决方案 </label>
        </div>
      </div>
      <div class="form-group">
        <label class="form-label">其他需求说明</label>
        <textarea name="otherDesc" class="form-textarea" placeholder="请简要描述您的合规管理需求或面临的挑战..."></textarea>
      </div>
      <div class="checkbox-group">
        <input type="checkbox" id="agreement" class="checkbox-input" required>
        <label for="agreement" class="checkbox-label"> 我已阅读并同意<a href="#">《服务条款》</a>和<a href="#">《隐私政策》</a>，
          并同意接收猫伯伯合规管家的产品资讯 </label>
      </div>
		<button type="submit" class="submit-btn">立即预约体验</button>
    </form>
  </div>
</div>

<!-- 成功提示弹窗 -->
<div class="success-modal" id="successModal">
  <div class="success-content">
    <div class="success-icon">🎉</div>
    <h3 class="success-title">预约成功！</h3>
    <p class="success-desc"> 感谢您的信任！我们的合规专家将尽快与您联系，
      为您提供专业的合规管理解决方案。 </p>
    <button class="success-btn" onclick="closeModal()">知道了</button>
  </div>
</div>

<!-- 错误提示弹窗 -->
<div class="error-modal" id="errorModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 10000; justify-content: center; align-items: center;">
  <div class="error-content" style="background: white; padding: 30px; border-radius: 12px; max-width: 400px; text-align: center; box-shadow: 0 20px 40px rgba(0,0,0,0.3);">
    <div class="error-icon" style="font-size: 48px; margin-bottom: 16px;">❌</div>
    <h3 class="error-title" style="color: #ef4444; margin-bottom: 12px; font-size: 20px;">提交失败</h3>
    <p class="error-desc" id="errorMessage" style="color: #666; margin-bottom: 24px; line-height: 1.5;"></p>
    <button class="error-btn" onclick="closeErrorModal()" style="background: #ef4444; color: white; border: none; padding: 12px 24px; border-radius: 6px; cursor: pointer; font-size: 16px;">确定</button>
  </div>
</div>
<script>
        // 生成粒子效果
        function createParticles() {
            const particlesContainer = document.getElementById('particles');
            const particleCount = 30;

            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.animationDelay = Math.random() * 20 + 's';
                particle.style.animationDuration = (15 + Math.random() * 10) + 's';
                particlesContainer.appendChild(particle);
            }
        }

        // 表单验证
        function validateForm(form) {
            const inputs = form.querySelectorAll('[required]');
            let isValid = true;

            inputs.forEach(input => {
                if (!input.value.trim()) {
                    isValid = false;
                    input.style.borderColor = '#ef4444';
                    input.addEventListener('input', function () {
                        this.style.borderColor = 'rgba(167, 139, 250, 0.3)';
                    });
                } else {
                    input.style.borderColor = 'rgba(167, 139, 250, 0.3)';
                }
            });

            // 验证手机号
            const phoneInput = form.querySelector('input[type="tel"]');
            const phoneRegex = /^1[3-9]\d{9}$/;
            if (phoneInput && !phoneRegex.test(phoneInput.value)) {
                isValid = false;
                phoneInput.style.borderColor = '#ef4444';
            }

            // 验证邮箱
            const emailInput = form.querySelector('input[type="email"]');
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (emailInput && !emailRegex.test(emailInput.value)) {
                isValid = false;
                emailInput.style.borderColor = '#ef4444';
            }

            // 验证协议勾选
            const agreement = form.querySelector('#agreement');
            if (!agreement.checked) {
                isValid = false;
                showErrorModal('请阅读并同意服务条款和隐私政策');
            }

            return isValid;
        }

        // 表单提交反馈
        document.getElementById('bookingForm').addEventListener('submit', function (e) {
            e.preventDefault();

            if (validateForm(this)) {
                // 显示提交动画
                const submitBtn = this.querySelector('.submit-btn');
                const originalText = submitBtn.textContent;
                submitBtn.textContent = '提交中...';
                submitBtn.disabled = true;

                // 收集表单数据
                const formData = new FormData(this);
                const data = {
                    name: formData.get('name') || '',
                    position: formData.get('position') || '',
                    mobile: formData.get('mobile') || '',
                    email: formData.get('email') || '',
                    company: formData.get('company') || '',
                    industry: formData.get('industry') || '',
                    companySize: formData.get('companySize') || '',
                    focusNeed: formData.get('focusNeed') || '',
                    otherDesc: formData.get('otherDesc') || ''
                };

                // 调用API
                fetch('http://localhost:8080/services/whiskerguardorgservice/api/reservations', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                })
                .then(response => {
                    if (response.status === 201) {
                        // 成功响应
                        return response.json().then(data => ({ success: true, data }));
                    } else {
                        // 错误响应
                        return response.json().then(data => ({ success: false, data }));
                    }
                })
                .then(result => {
                    if (result.success) {
                        // 显示成功弹窗
                        document.getElementById('successModal').style.display = 'flex';

                        // 重置表单
                        this.reset();

                        // 重置所有输入框边框颜色
                        this.querySelectorAll('input, select, textarea').forEach(input => {
                            input.style.borderColor = 'rgba(167, 139, 250, 0.3)';
                        });
                    } else {
                        // 显示错误信息
                        const errorTitle = result.data && result.data.title ? result.data.title : '提交失败，请稍后重试';
                        showErrorModal(errorTitle);
                    }
                })
                .catch(error => {
                    console.error('提交错误:', error);
                    showErrorModal('网络错误，请检查网络连接后重试');
                })
                .finally(() => {
                    // 恢复按钮状态
                    submitBtn.textContent = originalText;
                    submitBtn.disabled = false;
                });
            }
        });



        // 点击弹窗外部关闭
        document.getElementById('successModal').addEventListener('click', function (e) {
            if (e.target === this) {
                closeModal();
            }
        });

        // 点击错误弹窗外部关闭
        document.getElementById('errorModal').addEventListener('click', function (e) {
            if (e.target === this) {
                closeErrorModal();
            }
        });

        // 输入框聚焦效果
        const inputs = document.querySelectorAll('.form-input, .form-select, .form-textarea');
        inputs.forEach(input => {
            input.addEventListener('focus', function () {
                this.parentElement.style.transform = 'scale(1.02)';
            });

            input.addEventListener('blur', function () {
                this.parentElement.style.transform = 'scale(1)';
            });
        });

        // 实时验证手机号
        const phoneInput = document.querySelector('input[type="tel"]');
        if (phoneInput) {
            phoneInput.addEventListener('input', function (e) {
                // 只允许输入数字
                this.value = this.value.replace(/[^\d]/g, '');

                // 限制长度为11位
                if (this.value.length > 11) {
                    this.value = this.value.slice(0, 11);
                }
            });
        }

        // 添加输入动画
        const formGroups = document.querySelectorAll('.form-group');
        const observer = new IntersectionObserver((entries) => {
            entries.forEach((entry, index) => {
                if (entry.isIntersecting) {
                    setTimeout(() => {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }, index * 100);
                }
            });
        });

        formGroups.forEach(group => {
            group.style.opacity = '0';
            group.style.transform = 'translateY(20px)';
            group.style.transition = 'all 0.5s ease';
            observer.observe(group);
        });

        // 鼠标视差效果
        document.addEventListener('mousemove', (e) => {
            const x = (e.clientX / window.innerWidth - 0.5) * 2;
            const y = (e.clientY / window.innerHeight - 0.5) * 2;

            const shapes = document.querySelectorAll('.shape');
            shapes.forEach((shape, index) => {
                const speed = (index + 1) * 10;
                shape.style.transform = `translate(${x * speed}px, ${y * speed}px)`;
            });
        });

        // 添加加载动画
        window.addEventListener('load', () => {
            createParticles();
            document.body.style.opacity = '1';
        });

        // 防止重复提交
        let isSubmitting = false;
        const form = document.getElementById('bookingForm');
        form.addEventListener('submit', function (e) {
            if (isSubmitting) {
                e.preventDefault();
                return false;
            }
            isSubmitting = true;

            setTimeout(() => {
                isSubmitting = false;
            }, 3000);
        });

        // 添加键盘导航支持
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                // 关闭当前显示的弹窗
                const successModal = document.getElementById('successModal');
                const errorModal = document.getElementById('errorModal');

                if (successModal.style.display === 'flex') {
                    closeModal();
                } else if (errorModal.style.display === 'flex') {
                    closeErrorModal();
                }
            }
        });

        // 平滑滚动到错误输入框
        function scrollToError() {
            const errorInput = document.querySelector('input[style*="border-color: rgb(239, 68, 68)"]');
            if (errorInput) {
                errorInput.scrollIntoView({ behavior: 'smooth', block: 'center' });
                errorInput.focus();
            }
        }

        // 自动保存表单数据到本地存储
        const formInputs = form.querySelectorAll('input:not([type="checkbox"]):not([type="radio"]), select, textarea');

        // 恢复保存的数据
        formInputs.forEach(input => {
            const key = input.name || `${input.type}_${input.placeholder}`;
            const savedValue = localStorage.getItem(`booking_${key}`);
            if (savedValue) {
                input.value = savedValue;
            }
        });

        // 保存输入数据
        formInputs.forEach(input => {
            input.addEventListener('input', function () {
                const key = this.name || `${this.type}_${this.placeholder}`;
                localStorage.setItem(`booking_${key}`, this.value);
            });
        });

        // 成功提交后清除保存的数据
        function clearSavedData() {
            formInputs.forEach(input => {
                const key = input.name || `${input.type}_${input.placeholder}`;
                localStorage.removeItem(`booking_${key}`);
            });
        }

        // 修改成功弹窗的关闭函数
        function closeModal() {
            const modal = document.getElementById('successModal');
            modal.style.display = 'none';
            clearSavedData();
        }

        // 显示错误弹窗
        function showErrorModal(message) {
            const errorModal = document.getElementById('errorModal');
            const errorMessage = document.getElementById('errorMessage');
            errorMessage.textContent = message;
            errorModal.style.display = 'flex';
        }

        // 关闭错误弹窗
        function closeErrorModal() {
            const errorModal = document.getElementById('errorModal');
            errorModal.style.display = 'none';
        }

        // 美化选择框
        const selects = document.querySelectorAll('.form-select');
        selects.forEach(select => {
            select.addEventListener('change', function () {
                if (this.value) {
                    this.style.color = 'white';
                } else {
                    this.style.color = 'rgba(255, 255, 255, 0.4)';
                }
            });
        });

        console.log('猫伯伯合规管家预约体验页面已加载完成！');
    </script>
</body>
</html>