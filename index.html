<!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>猫伯伯智能合规管家</title>
<script src="js/gsap.min.js"></script>
<script src="js/three.min.js"></script>
<link rel="icon" href="img/logo2.png" type="image/png">
<link rel="stylesheet" href="img/index.css">
<link rel="stylesheet" href="img/hero.css">
<link rel="stylesheet" href="font/iconfont.css">
</head>

<body>
<!-- 加载动画 -->
<div class="loader" id="loader">
  <div class="loader-content">
    <div class="loader-logo"><img src="img/logo.png" alt="猫伯伯智能合规管家"></div>
    <div class="loader-text">猫伯伯智能合规管家</div>
  </div>
</div>

<!-- 超现代导航栏 -->
<nav class="navbar" id="navbar">
  <div class="nav-container"> <a href="#" class="logo">
    <div class="logo-icon"><img src="img/logo-white.png" alt="猫伯伯智能合规管家"></div>
    猫伯伯 </a>
    <ul class="nav-links">
      <li><a href="#home">首页</a></li>
      <li><a href="#value">价值</a></li>
      <li><a href="#product">产品</a></li>
      <li><a href="#success">案例</a></li>
    </ul>
    <a href="#cta" class="cta-button">立即开始</a> </div>
</nav>

<!-- Hero Section -->
<section class="hero" id="home"> 
  <!-- 粒子背景 -->
  <div class="particles" id="particles"></div>
  
  <!-- 网格背景 -->
  <div class="grid-bg"></div>
  <div class="hero-container">
    <div class="hero-content">
      <h1 class="gradient-text"> <span class="light-decoration"></span> 让合规成为企业 <br>
        增长的新引擎 </h1>
      <div class="hero-subtitle"> <span class="typing-text" id="typing-text">AI驱动的合规管理新时代，为企业构建全方位智能防护网</span> </div>
      <div class="hero-buttons"> <a href="book-form.html" class="btn btn-primary magnetic">申请7天免费体验</a> <a href="book-form.html" class="btn btn-secondary magnetic">预约演示</a> </div>
      <div class="stats">
        <div class="stat-item">
          <div class="stat-number">5年+</div>
          <div class="stat-label">专业合规经验</div>
        </div>
        <div class="stat-item">
          <div class="stat-number">3倍</div>
          <div class="stat-label">效率提升目标</div>
        </div>
        <div class="stat-item">
          <div class="stat-number">70%</div>
          <div class="stat-label">成本节约空间</div>
        </div>
      </div>
    </div>
    <div class="hero-visual">
      <div class="hero-showcase">
        <div class="main-dashboard">
          <div class="holographic-effect"></div>
          <div class="dashboard-header">
            <div class="dashboard-title">合规智能驾驶舱</div>
            <div class="dashboard-status">
              <div class="status-dot"></div>
              <span style="color: rgba(255,255,255,0.6); font-size: 14px;">系统运行正常</span> </div>
          </div>
          <div class="dashboard-content">
            <div class="data-card">
              <div class="card-icon">📊</div>
              <div class="card-value">85</div>
              <div class="card-label">合规指数</div>
            </div>
            <div class="data-card">
              <div class="card-icon">⚡</div>
              <div class="card-value">12</div>
              <div class="card-label">待处理事项</div>
            </div>
            <div class="data-card">
              <div class="card-icon">🛡️</div>
              <div class="card-value">3</div>
              <div class="card-label">风险预警</div>
            </div>
            <div class="data-card">
              <div class="card-icon">📈</div>
              <div class="card-value">96%</div>
              <div class="card-label">制度覆盖率</div>
            </div>
            <div class="data-card">
              <div class="card-icon">🔍</div>
              <div class="card-value">847</div>
              <div class="card-label">监测法规</div>
            </div>
            <div class="data-card">
              <div class="card-icon">✅</div>
              <div class="card-value">99.5%</div>
              <div class="card-label">系统可用性</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 浮动元素 -->
    <div class="floating-elements">
      <div class="floating-card">
        <div class="floating-text">新法规更新</div>
        <div class="floating-value">3条待查看</div>
      </div>
      <div class="floating-card">
        <div class="floating-text">AI识别准确率</div>
        <div class="floating-value">95.8%</div>
      </div>
    </div>
  </div>
</section>

<!-- 价值主张区域 -->
<section class="value-section" id="value">
  <div class="value-container">
    <div class="section-badge">为什么选择猫伯伯</div>
    <h2 class="section-title">合规管理，原来可以这么简单</h2>
    <p class="section-subtitle">我们将律师团队的专业合规经验与AI技术深度融合，让复杂的法规变成简单的操作，让被动的应对变成主动的预防。</p>
    <div class="value-grid">
      <div class="value-card glow-effect">
        <div class="value-icon">🧠</div>
        <h3>智能法规解读</h3>
        <p>再也不用花时间研读复杂的法律条文，AI帮您快速理解法律条文，将复杂法规转化为企业可执行的具体制度。</p>
      </div>
      <div class="value-card glow-effect">
        <div class="value-icon">⚡</div>
        <h3>实时风险预警</h3>
        <p>智能监控企业运营状态，提前发现潜在风险，让您从容应对，避免违规事件发生。</p>
      </div>
      <div class="value-card glow-effect">
        <div class="value-icon">🎯</div>
        <h3>精准责任到人</h3>
        <p>清晰定义每个岗位的合规职责，让每个员工都知道自己该做什么，不该做什么。</p>
      </div>
      <div class="value-card glow-effect">
        <div class="value-icon">📊</div>
        <h3>可视化管理驾驶舱</h3>
        <p>一目了然的合规状态展示，帮助管理层快速决策，让合规管理变得透明高效。</p>
      </div>
      <div class="value-card glow-effect">
        <div class="value-icon">🛡️</div>
        <h3>7×24小时智能守护</h3>
        <p>系统智能助手全天候在线，随时协助解答合规问题，提供专业建议，像拥有了专业法务支持。</p>
      </div>
      <div class="value-card glow-effect">
        <div class="value-icon">🚀</div>
        <h3>灵活配置定制</h3>
        <p>据企业实际需求灵活配置功能模块，提供个性化的合规管理解决方案。</p>
      </div>
    </div>
  </div>
</section>

<!-- 问题-解决方案对比 -->
<section class="problem-solution">
  <div class="problem-solution-container">
    <div class="before-after">
      <div class="problem-side">
        <h2 class="problem-title">传统合规管理的痛点</h2>
        <ul class="problem-list">
          <li>法规更新频繁，人工跟踪困难</li>
          <li>制度文件分散，查找费时费力</li>
          <li>风险识别依赖经验，容易遗漏</li>
          <li>合规培训形式化，效果有限</li>
          <li>违规事件被动发现，损失已成</li>
          <li>责任不清晰，问责困难</li>
        </ul>
      </div>
      <div class="solution-side">
        <h2 class="solution-title">猫伯伯的智能解决方案</h2>
        <ul class="solution-list">
          <li>AI自动监测法规变化，及时推送影响分析</li>
          <li>智能制度库，秒级检索，关联推荐</li>
          <li>AI风险识别，全面覆盖，精准预警</li>
          <li>个性化培训方案，互动学习，效果可视</li>
          <li>实时监控预警，主动防范，有效降低损失风险</li>
          <li>清晰责任矩阵，自动追溯，公正透明</li>
        </ul>
      </div>
    </div>
  </div>
</section>

<!-- 产品功能展示 -->
<section class="product-showcase" id="product">
  <div class="showcase-container">
    <div class="showcase-header">
      <div class="section-badge">核心功能</div>
      <h2 class="section-title">一个平台 解决企业合规难题</h2>
      <p class="section-subtitle"> 基于"一体三翼"架构设计，覆盖合规管理全生命周期，让每个环节都智能高效。</p>
    </div>
    <div class="feature-tabs">
      <button class="tab-button active" data-tab="制度管理"><span class="iconfont">&#xe851;</span> 智能制度管理</button>
      <button class="tab-button" data-tab="风险识别"><span class="iconfont">&#xe672;</span> 风险智能识别</button>
      <button class="tab-button" data-tab="实时监控"><span class="iconfont">&#xe623;</span> 实时监控预警</button>
      <button class="tab-button" data-tab="智能审查"><span class="iconfont">&#xe6ac;</span> AI智能审查</button>
    </div>
    <div class="feature-content active" id="制度管理">
      <div class="feature-info">
        <h3>制度管理从此告别繁琐</h3>
        <p>AI自动将复杂的法律法规转化为企业可执行的内部制度，实时监测法规变化，智能提醒影响评估，让制度管理变得简单高效。</p>
        <ul class="feature-benefits">
          <li>法规动态更新，及时推送</li>
          <li>制度冲突智能检测</li>
          <li>一键生成合规报告</li>
          <li>制度执行效果跟踪</li>
        </ul>
        <a href="book-form.html" class="btn-hero-primary"
                        style="background: var(--gradient-primary); color: white;">申请体验</a> </div>
      <div class="feature-visual-bg">
        <div class="feature-visual visual-1">
          <div class="doc-flow">
            <div class="doc-node">
              <div class="doc-icon">📜</div>
              <div class="doc-text">法规采集</div>
            </div>
            <div class="flow-arrow">→</div>
            <div class="doc-node">
              <div class="doc-icon">🤖</div>
              <div class="doc-text">智能转化</div>
            </div>
            <div class="flow-arrow">→</div>
            <div class="doc-node">
              <div class="doc-icon">📋</div>
              <div class="doc-text">制度生成</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="feature-content" id="风险识别">
      <div class="feature-info">
        <h3>风险识别精准到位</h3>
        <p>基于大数据分析和AI算法，自动识别企业各环节的合规风险点，建立三张清单管理体系，让风险无处遁形。</p>
        <ul class="feature-benefits">
          <li>AI智能风险扫描</li>
          <li>风险等级自动评估</li>
          <li>岗位责任精准匹配</li>
          <li>风险趋势预测分析</li>
        </ul>
        <a href="book-form.html" class="btn-hero-primary"
                        style="background: var(--gradient-primary); color: white;">申请体验</a> </div>
      <div class="feature-visual-bg">
        <div class="feature-visual visual-2">
          <div class="radar-system">
            <div class="radar-circle"></div>
            <div class="radar-circle"></div>
            <div class="radar-circle"></div>
            <div class="radar-sweep"></div>
            <div class="risk-dot risk-high" style="top: 20%; left: 30%;"></div>
            <div class="risk-dot risk-medium" style="top: 60%; right: 25%;"></div>
            <div class="risk-dot risk-low" style="bottom: 30%; left: 40%;"></div>
          </div>
          <div class="risk-stats">
            <div class="risk-stat"> <span class="risk-value" style="color: #ef4444;">3</span> <span class="risk-label">高风险</span> </div>
            <div class="risk-stat"> <span class="risk-value" style="color: #f59e0b;">7</span> <span class="risk-label">中风险</span> </div>
            <div class="risk-stat"> <span class="risk-value" style="color: #10b981;">15</span> <span class="risk-label">低风险</span> </div>
          </div>
        </div>
      </div>
    </div>
    <div class="feature-content" id="实时监控">
      <div class="feature-info">
        <h3>合规状态一目了然</h3>
        <p>可视化合规驾驶舱，实时展示企业合规状态，异常自动预警，让管理层随时掌握合规动态，及时做出决策。</p>
        <ul class="feature-benefits">
          <li>实时数据可视化展示</li>
          <li>多维度风险分析</li>
          <li>智能预警推送</li>
          <li>决策支持报告</li>
        </ul>
        <a href="book-form.html" class="btn-hero-primary"
                        style="background: var(--gradient-primary); color: white;">申请体验</a> </div>
      <div class="feature-visual-bg">
        <div class="feature-visual visual-3">
          <div class="monitor-card">
            <div class="monitor-header">
              <div class="monitor-title">合规指数</div>
              <div class="monitor-status"></div>
            </div>
            <div class="monitor-chart">
              <div class="chart-line"></div>
            </div>
            <div>
              <div class="monitor-value">92%</div>
              <div class="monitor-label">整体合规率</div>
            </div>
          </div>
          <div class="monitor-card">
            <div class="monitor-header">
              <div class="monitor-title">风险监控</div>
              <div class="monitor-status" style="background: #f59e0b;"></div>
            </div>
            <div class="monitor-chart">
              <div class="chart-line"></div>
            </div>
            <div>
              <div class="monitor-value">24</div>
              <div class="monitor-label">待处理事项</div>
            </div>
          </div>
          <div class="monitor-card">
            <div class="monitor-header">
              <div class="monitor-title">制度覆盖</div>
              <div class="monitor-status"></div>
            </div>
            <div class="monitor-chart">
              <div class="chart-line"></div>
            </div>
            <div>
              <div class="monitor-value">847</div>
              <div class="monitor-label">监测法规数</div>
            </div>
          </div>
          <div class="monitor-card">
            <div class="monitor-header">
              <div class="monitor-title">系统健康</div>
              <div class="monitor-status"></div>
            </div>
            <div class="monitor-chart">
              <div class="chart-line"></div>
            </div>
            <div>
              <div class="monitor-value">20+</div>
              <div class="monitor-label">功能模块</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="feature-content" id="智能审查">
      <div class="feature-info">
        <h3>AI助手7×24小时在线</h3>
        <p>智能合规助手随时在线，自动审查合同文件，识别风险条款，提供专业建议，如同拥有专业法务团队。</p>
        <ul class="feature-benefits">
          <li>合同条款智能审查</li>
          <li>风险点自动标注</li>
          <li>修改建议实时推送</li>
          <li>合规咨询随时响应</li>
        </ul>
        <a href="book-form.html" class="btn-hero-primary"
                        style="background: var(--gradient-primary); color: white;">申请体验</a> </div>
      <div class="feature-visual-bg">
        <div class="feature-visual visual-4">
          <div class="ai-assistant">
            <div class="ai-avatar">🤖</div>
            <div class="ai-status">● AI合规助手在线</div>
            <div class="ai-stats">
              <div class="ai-stat"> <span class="ai-stat-value">98%</span> <span class="ai-stat-label">准确率</span> </div>
              <div class="ai-stat"> <span class="ai-stat-value">3秒</span> <span class="ai-stat-label">响应时间</span> </div>
              <div class="ai-stat"> <span class="ai-stat-value">24/7</span> <span class="ai-stat-label">在线服务</span> </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- 客户成功案例 -->
<section class="success-section" id="success">
  <div class="success-container">
    <div class="section-badge">合规服务案例 </div>
    <h2 class="section-title" style="color: white;">他们的成功，就是我们的底气</h2>
    <p class="section-subtitle" style="color: rgba(255,255,255,0.8);">看看这些企业如何通过猫伯伯团队专业服务实现合规管理优化 </p>
    <div class="success-grid">
      <div class="success-card">
        <div class="success-quote"> "猫伯伯律师团队帮助我们建立了完整的合规管理体系，从风险识别到制度建设，全程提供专业指导，大幅提升了合规管理效率。" </div>
        <div class="success-author">
          <div class="author-avatar">张</div>
          <div class="author-info">
            <h4>张总</h4>
            <p>某制造企业 总经理</p>
          </div>
        </div>
        <div class="success-metrics">
          <div class="metric"> <span class="metric-number">60%</span> <span class="metric-label">效率提升</span> </div>
          <div class="metric"> <span class="metric-number">95%</span> <span class="metric-label">风险识别率</span> </div>
          <div class="metric"> <span class="metric-number">30+</span> <span class="metric-label">制度建设数量</span> </div>
        </div>
      </div>
      <div class="success-card">
        <div class="success-quote"> "在重大合同签署前，猫伯伯律师团队的专业审查帮我们识别了大量风险点，避免了潜在的法律损失，保障了公司利益。" </div>
        <div class="success-author">
          <div class="author-avatar">李</div>
          <div class="author-info">
            <h4>李总</h4>
            <p>某贸易公司 法务部长</p>
          </div>
        </div>
        <div class="success-metrics">
          <div class="metric"> <span class="metric-number">3倍</span> <span class="metric-label">审查速度</span> </div>
          <div class="metric"> <span class="metric-number">200+</span> <span class="metric-label">合同审查数</span> </div>
          <div class="metric"> <span class="metric-number">500万</span> <span class="metric-label">避免损失</span> </div>
        </div>
      </div>
      <div class="success-card">
        <div class="success-quote"> "猫伯伯律师团队协助我们建立了完善的内部合规制度体系，规范了业务流程，显著提升了企业规范化管理水平。" </div>
        <div class="success-author">
          <div class="author-avatar">王</div>
          <div class="author-info">
            <h4>王总</h4>
            <p>某省属国企 风控总监</p>
          </div>
        </div>
        <div class="success-metrics">
          <div class="metric"> <span class="metric-number">100%</span> <span class="metric-label">覆盖率</span> </div>
          <div class="metric"> <span class="metric-number">80%</span> <span class="metric-label">风险识别准确率</span> </div>
          <div class="metric"> <span class="metric-number">50+</span> <span class="metric-label">规范业务流程</span> </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- 超级CTA区域 -->
<section class="super-cta" id="cta">
  <div class="super-cta-container">
    <h2>让合规变得简单 高效 省心</h2>
    <p>拥有专属的"合规管家"，您专注核心业务，合规管理交给我们<br>
      现在开始，7天免费体验，无风险试用 </p>
    <div class="super-cta-buttons"> <a href="book-form.html" class="btn-super btn-super-primary">申请免费体验</a> <a href="book-form.html" class="btn-super btn-super-secondary">预约演示</a> </div>
    <div class="contact-info">
      <div class="contact-item"> <img src="img/dianhua.png" alt=""><a href="tel:4001665291" style="color: white; text-decoration: none" >************</a> </div>
      <div class="contact-item"> <img src="img/youxiang.png" alt=""><a href="mailto:<EMAIL>" style="color: white; text-decoration: none" > <EMAIL></a> </div>
      <div class="contact-item"> <img src="img/weixin.png" alt="">微信公众号：猫伯伯合规管家 </div>
    </div>
  </div>
</section>

<!-- 现代化页脚 -->
<footer class="footer">
  <div class="footer-container">
    <div class="footer-content">
      <div class="footer-brand">
        <div class="footer-logo">
          <div class="footer-logo-icon"><img src="img/logo-white.png" alt="猫伯伯智能合规管家"></div>
          猫伯伯智能合规管家 </div>
        <p class="footer-desc"> 专业的企业合规管理智能化解决方案提供商，致力于通过AI技术创新推动企业合规管理现代化，让每个企业都能轻松拥有专业的合规管理能力。 </p>
        <p style="font-weight: 600; color: var(--accent);"> 让管理插上合规的翅膀<br>
          让合规插上科技的翅膀 </p>
      </div>
      <div class="footer-section">
        <h3>产品功能</h3>
        <ul class="footer-links">
          <li><a href="#product">智能制度管理</a></li>
          <li><a href="#product">风险智能识别</a></li>
          <li><a href="#product">实时监控预警</a></li>
          <li><a href="#product">AI智能审查</a></li>
          <li><a href="#product">合规培训体系</a></li>
        </ul>
      </div>
      <div class="footer-section">
        <h3>服务支持</h3>
        <ul class="footer-links">
          <li><a href="#success">客户案例</a></li>
          <li><a href="#help">帮助中心</a></li>
          <li><a href="#docs">产品文档</a></li>
          <li><a href="#training">培训服务</a></li>
          <li><a href="#support">技术支持</a></li>
        </ul>
      </div>
      <div class="footer-section">
        <h3>联系我们</h3>
        <ul class="footer-links">
          <li class="foot-link-ico"><img src="img/dianhua1.png" alt=""><a href="tel:4001665291" style="color: white; text-decoration: none" >************</a></li>
          <li class="foot-link-ico"><img src="img/youxiang1.png" alt=""><a href="mailto:<EMAIL>" style="color: white; text-decoration: none" > <EMAIL></a> </li>
          <li class="foot-link-ico"><img src="img/dizhi1.png" alt="">苏州高新区鹿山路369号39幢627室</li>
          <li class="foot-link-ico"><img src="img/weixin2.png" alt="">微信公众号：猫伯伯合规管家</li>
        </ul>
        <span class="bg-white" style="display: inline-block;border-radius: 4px; overflow: hidden; width:80px; height: 80px;"><img src="img/qrcode.jpg" width="80" height="80" style="display: inline; overflow: hidden" /></span> </div>
    </div>
    <div class="footer-bottom">
      <p>&copy; 2025 猫伯伯合规管家 WhiskerGuard. 保留所有权利</p>
      <p><a href="https://beian.miit.gov.cn" target="_blank" rel="nofollow"
                        style="color: #888888; text-decoration: none">苏ICP备2025166477号</a></p>
    </div>
  </div>
</footer>
<script>
        // 页面加载完成后隐藏加载动画
        window.addEventListener('load', () => {
            setTimeout(() => {
                document.getElementById('loader').classList.add('hidden');
            }, 400);
        });

        // 导航栏滚动效果
        window.addEventListener('scroll', () => {
            const navbar = document.getElementById('navbar');
            if (window.scrollY > 100) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }
        });

        // 功能标签切换
        document.querySelectorAll('.tab-button').forEach(button => {
            button.addEventListener('click', () => {
                // 移除所有激活状态
                document.querySelectorAll('.tab-button').forEach(btn => btn.classList.remove('active'));
                document.querySelectorAll('.feature-content').forEach(content => content.classList.remove('active'));

                // 添加当前激活状态
                button.classList.add('active');
                const tabName = button.getAttribute('data-tab');
                document.getElementById(tabName).classList.add('active');
            });
        });

        // Three.js 3D效果
        function init3D() {
            const canvas = document.getElementById('three-canvas');
            const scene = new THREE.Scene();
            const camera = new THREE.PerspectiveCamera(75, canvas.clientWidth / canvas.clientHeight, 0.1, 1000);
            const renderer = new THREE.WebGLRenderer({ canvas: canvas, alpha: true });

            renderer.setSize(canvas.clientWidth, canvas.clientHeight);
            renderer.setClearColor(0x000000, 0);

            // 创建几何体
            const geometry = new THREE.SphereGeometry(1, 32, 32);
            const material = new THREE.MeshPhongMaterial({
                color: 0x6366f1,
                transparent: true,
                opacity: 0.8,
                shininess: 100
            });
            const sphere = new THREE.Mesh(geometry, material);
            scene.add(sphere);

            // 添加光源
            const light = new THREE.PointLight(0xffffff, 1, 100);
            light.position.set(10, 10, 10);
            scene.add(light);

            const ambientLight = new THREE.AmbientLight(0x404040, 0.5);
            scene.add(ambientLight);

            camera.position.z = 3;

            // 动画循环
            function animate() {
                requestAnimationFrame(animate);
                sphere.rotation.x += 0.01;
                sphere.rotation.y += 0.01;
                renderer.render(scene, camera);
            }
            animate();

            // 响应式调整
            window.addEventListener('resize', () => {
                camera.aspect = canvas.clientWidth / canvas.clientHeight;
                camera.updateProjectionMatrix();
                renderer.setSize(canvas.clientWidth, canvas.clientHeight);
            });
        }

        // 初始化3D效果
        setTimeout(init3D, 1500);

        // GSAP动画
        gsap.registerPlugin();

        // 滚动触发动画
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const element = entry.target;

                    if (element.classList.contains('value-card')) {
                        gsap.from(element, {
                            duration: 0.8,
                            y: 20,
                            // opacity: 0,
                            ease: "power2.out",
                            delay: Math.random() * 0.3
                        });
                    }

                    if (element.classList.contains('success-card')) {
                        gsap.from(element, {
                            duration: 0.8,
                            scale: 0.9,
                            // opacity: 0,
                            ease: "power2.out",
                            delay: Math.random() * 0.2
                        });
                    }

                    if (element.classList.contains('stat-card')) {
                        gsap.from(element, {
                            duration: 0.6,
                            y: 30,
                            // opacity: 0,
                            ease: "power2.out",
                            delay: Math.random() * 0.1
                        });

                        // 数字动画
                        const numberElement = element.querySelector('.stat-number');
                        if (numberElement) {
                            const finalNumber = numberElement.textContent;
                            if (!isNaN(parseFloat(finalNumber))) {
                                gsap.from(numberElement, {
                                    duration: 2,
                                    textContent: 0,
                                    roundProps: "textContent",
                                    ease: "power2.out"
                                });
                            }
                        }
                    }
                }
            });
        }, observerOptions);

        // 观察需要动画的元素
        document.querySelectorAll('.value-card, .success-card, .stat-card, .metric').forEach(el => {
            observer.observe(el);
        });

        // 平滑滚动
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // 鼠标跟随光标效果
        document.addEventListener('mousemove', (e) => {
            const cursor = document.querySelector('.custom-cursor');
            if (!cursor) {
                const newCursor = document.createElement('div');
                newCursor.className = 'custom-cursor';
                newCursor.style.cssText = `
                    position: fixed;
                    width: 20px;
                    height: 20px;
                    background: radial-gradient(circle, rgba(99, 102, 241, 0.8) 0%, transparent 70%);
                    border-radius: 50%;
                    pointer-events: none;
                    z-index: 9999;
                    transition: transform 0.1s ease;
                    mix-blend-mode: difference;
                `;
                document.body.appendChild(newCursor);
            }

            const cursorElement = document.querySelector('.custom-cursor');
            cursorElement.style.left = e.clientX - 10 + 'px';
            cursorElement.style.top = e.clientY - 10 + 'px';
        });

        // 增强悬停效果
        document.querySelectorAll('.value-card, .success-card, .btn-hero-primary, .btn-hero-secondary').forEach(element => {
            element.addEventListener('mouseenter', () => {
                gsap.to(element, { duration: 0.3, scale: 1.02, ease: "power2.out" });
            });

            element.addEventListener('mouseleave', () => {
                gsap.to(element, { duration: 0.3, scale: 1, ease: "power2.out" });
            });
        });

        // 响应式菜单
        function createMobileMenu() {
            if (window.innerWidth <= 768) {
                const navbar = document.querySelector('.nav-container');
                let mobileMenuBtn = document.querySelector('.mobile-menu-btn');

                if (!mobileMenuBtn) {
                    mobileMenuBtn = document.createElement('button');
                    mobileMenuBtn.innerHTML = '☰';
                    mobileMenuBtn.className = 'mobile-menu-btn';
                    mobileMenuBtn.style.cssText = `
                        background: none;
                        border: none;
                        font-size: 24px;
                        color: white;
                        cursor: pointer;
                        padding: 8px;
                        border-radius: 8px;
                        transition: all 0.3s ease;
                    `;

                    mobileMenuBtn.onclick = () => {
                        const navLinks = document.querySelector('.nav-links');
                        if (navLinks.style.display === 'flex') {
                            navLinks.style.display = 'none';
                            mobileMenuBtn.innerHTML = '☰';
                        } else {
                            navLinks.style.display = 'flex';
                            navLinks.style.flexDirection = 'column';
                            navLinks.style.position = 'absolute';
                            navLinks.style.top = '60px';
                            navLinks.style.left = '0';
                            navLinks.style.right = '0';
                            navLinks.style.background = 'rgba(0, 0, 0, 0.95)';
                            navLinks.style.padding = '20px';
                            navLinks.style.borderRadius = '0 0 20px 20px';
                            mobileMenuBtn.innerHTML = '✕';
                        }
                    };

                    navbar.appendChild(mobileMenuBtn);
                }
            }
        }

        // 窗口大小改变时检查移动端菜单
        window.addEventListener('resize', createMobileMenu);
        createMobileMenu();

        // 页面性能优化
        const images = document.querySelectorAll('img');
        images.forEach(img => {
            img.loading = 'lazy';
        });

        // 添加Three.js初始化代码
        // Three.js 3D效果
        function init3D() {
            const canvas = document.getElementById('three-canvas');
            const scene = new THREE.Scene();
            const camera = new THREE.PerspectiveCamera(75, canvas.clientWidth / canvas.clientHeight, 0.1, 1000);
            const renderer = new THREE.WebGLRenderer({ canvas: canvas, alpha: true });

            renderer.setSize(canvas.clientWidth, canvas.clientHeight);
            renderer.setClearColor(0x000000, 0);

            // 创建几何体
            const geometry = new THREE.SphereGeometry(1, 32, 32);
            const material = new THREE.MeshPhongMaterial({
                color: 0x6366f1,
                transparent: true,
                opacity: 0.8,
                shininess: 100
            });
            const sphere = new THREE.Mesh(geometry, material);
            scene.add(sphere);

            // 添加光源
            const light = new THREE.PointLight(0xffffff, 1, 100);
            light.position.set(10, 10, 10);
            scene.add(light);

            const ambientLight = new THREE.AmbientLight(0x404040, 0.5);
            scene.add(ambientLight);

            camera.position.z = 3;

            // 动画循环
            function animate() {
                requestAnimationFrame(animate);
                sphere.rotation.x += 0.01;
                sphere.rotation.y += 0.01;
                renderer.render(scene, camera);
            }
            animate();
        }

        // 添加键盘导航支持
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Tab') {
                document.body.classList.add('keyboard-navigation');
            }
        });

        document.addEventListener('mousedown', () => {
            document.body.classList.remove('keyboard-navigation');
        });

        // 初始化3D效果
        setTimeout(init3D, 1500);

        // 错误处理
        window.addEventListener('error', (e) => {
            console.log('页面加载出现问题，但不影响基本功能使用');
        });

        // 增加页面交互性
        document.querySelectorAll('.floating-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                // 创建点击波纹效果
                const ripple = document.createElement('div');
                ripple.style.cssText = `
                    position: absolute;
                    border-radius: 50%;
                    background: rgba(255, 255, 255, 0.6);
                    width: 100px;
                    height: 100px;
                    left: ${e.offsetX - 50}px;
                    top: ${e.offsetY - 50}px;
                    animation: ripple 0.6s ease-out;
                    pointer-events: none;
                `;

                btn.style.position = 'relative';
                btn.style.overflow = 'hidden';
                btn.appendChild(ripple);

                setTimeout(() => {
                    ripple.remove();
                }, 600);
            });
        });

        // 添加页面滚动进度条
        const progressBar = document.createElement('div');
        progressBar.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 0%;
            height: 3px;
            background: linear-gradient(90deg, #6366f1, #8b5cf6, #06b6d4);
            z-index: 10001;
            transition: width 0.3s ease;
        `;
        document.body.appendChild(progressBar);

        window.addEventListener('scroll', () => {
            const winScroll = document.body.scrollTop || document.documentElement.scrollTop;
            const height = document.documentElement.scrollHeight - document.documentElement.clientHeight;
            const scrolled = (winScroll / height) * 100;
            progressBar.style.width = scrolled + '%';
        });

        // CSS动画关键帧
        const styleSheet = document.createElement('style');
        styleSheet.textContent = `
            @keyframes ripple {
                to {
                    transform: scale(4);
                    opacity: 0;
                }
            }
            
            .keyboard-navigation button:focus,
            .keyboard-navigation a:focus {
                outline: 2px solid #6366f1;
                outline-offset: 2px;
            }
            
            .custom-cursor {
                transition: transform 0.1s ease;
            }
            
            .value-card:hover .value-icon {
                transform: rotate(5deg) scale(1.1);
                transition: transform 0.3s ease;
            }
            
            .parallax-element {
                will-change: transform;
            }
        `;
        document.head.appendChild(styleSheet);

        console.log('猫伯伯合规管家官网已加载完成！');


        // 生成粒子
        function createParticles() {
            const particlesContainer = document.getElementById('particles');
            const particleCount = 50;

            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.animationDelay = Math.random() * 20 + 's';
                particle.style.animationDuration = (15 + Math.random() * 10) + 's';
                particlesContainer.appendChild(particle);
            }
        }

        // 打字机效果
        function typeWriter() {
            const texts = [
                "AI驱动的合规管理新时代，为企业构建全方位智能防护网",
                "让每个企业都能轻松拥有专业的合规管理能力",
                "从被动应对到主动预防，开启企业合规管理新纪元"
            ];
            let textIndex = 0;
            let charIndex = 0;
            let isDeleting = false;
            const typingElement = document.getElementById('typing-text');

            function type() {
                const currentText = texts[textIndex];

                if (isDeleting) {
                    typingElement.textContent = currentText.substring(0, charIndex - 1);
                    charIndex--;
                } else {
                    typingElement.textContent = currentText.substring(0, charIndex + 1);
                    charIndex++;
                }

                if (!isDeleting && charIndex === currentText.length) {
                    setTimeout(() => {
                        isDeleting = true;
                    }, 2000);
                } else if (isDeleting && charIndex === 0) {
                    isDeleting = false;
                    textIndex = (textIndex + 1) % texts.length;
                }

                const typingSpeed = isDeleting ? 30 : 50;
                setTimeout(type, typingSpeed);
            }

            type();
        }

        // 数字动画
        function animateNumbers() {
            const numbers = document.querySelectorAll('.stat-number');

            numbers.forEach(number => {
                const target = parseInt(number.getAttribute('data-value'));
                const duration = 2000;
                const increment = target / (duration / 16);
                let current = 0;

                const updateNumber = () => {
                    current += increment;
                    if (current < target) {
                        number.textContent = Math.floor(current) + (number.getAttribute('data-value').includes('%') ? '%' : '+');
                        requestAnimationFrame(updateNumber);
                    } else {
                        number.textContent = target + (number.getAttribute('data-value').includes('%') ? '%' : '+');
                    }
                };

                // 延迟启动动画
                setTimeout(updateNumber, 500);
            });
        }

        // 磁性按钮效果
        function magneticButtons() {
            const magneticElements = document.querySelectorAll('.magnetic');

            magneticElements.forEach(elem => {
                elem.addEventListener('mousemove', (e) => {
                    const rect = elem.getBoundingClientRect();
                    const x = e.clientX - rect.left - rect.width / 2;
                    const y = e.clientY - rect.top - rect.height / 2;

                    elem.style.transform = `translate(${x * 0.3}px, ${y * 0.3}px)`;
                });

                elem.addEventListener('mouseleave', () => {
                    elem.style.transform = 'translate(0, 0)';
                });
            });
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', () => {
            createParticles();
            typeWriter();
            animateNumbers();
            magneticButtons();
        });

        // 视差效果
        document.addEventListener('mousemove', (e) => {
            const x = (e.clientX / window.innerWidth - 0.5) * 2;
            const y = (e.clientY / window.innerHeight - 0.5) * 2;

            const showcase = document.querySelector('.hero-showcase');
            if (showcase) {
                showcase.style.transform = `translateY(${-20 + y * 5}px) rotateY(${-10 + x * 5}deg) rotateX(${5 + y * 3}deg)`;
            }
        });


    </script>
</body>
</html>