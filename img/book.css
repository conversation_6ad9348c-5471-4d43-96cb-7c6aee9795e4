:root {
    --primary: #6366f1;
    --primary-dark: #4338ca;
    --secondary: #06b6d4;
    --accent: #f59e0b;
    --success: #10b981;
    --danger: #ef4444;
    --dark: #0f172a;
    --light: #f8fafc;
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --gradient-primary: linear-gradient(135deg, #6366f1 0%, #8b5cf6 50%, #06b6d4 100%);
    --gradient-bg: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --glass: rgba(255, 255, 255, 0.1);
    --glass-border: rgba(255, 255, 255, 0.2);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
    line-height: 1.6;
    color: #ffffff;
    background: #000000;
    overflow-x: hidden;
    min-height: 100vh;
}

/* 背景效果 */
.page-background {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(ellipse at 20% 80%, rgba(167, 139, 250, 0.3) 0%, transparent 40%),
        radial-gradient(ellipse at 80% 20%, rgba(236, 72, 153, 0.2) 0%, transparent 40%),
        radial-gradient(ellipse at 50% 50%, rgba(96, 165, 250, 0.2) 0%, transparent 60%),
        linear-gradient(180deg, #000000 0%, #0a0014 50%, #1a0033 100%);
    z-index: -2;
}

/* 动态网格 */
.grid-bg {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        linear-gradient(rgba(167, 139, 250, 0.1) 1px, transparent 1px),
        linear-gradient(90deg, rgba(167, 139, 250, 0.1) 1px, transparent 1px);
    background-size: 50px 50px;
    transform: perspective(500px) rotateX(60deg) scale(2);
    transform-origin: center center;
    opacity: 0.3;
    z-index: -1;
    animation: grid-move 20s linear infinite;
}

@keyframes grid-move {
    0% {
        transform: perspective(500px) rotateX(60deg) scale(2) translateY(0);
    }

    100% {
        transform: perspective(500px) rotateX(60deg) scale(2) translateY(50px);
    }
}

/* 粒子效果 */
.particles {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: -1;
}

.particle {
    position: absolute;
    width: 2px;
    height: 2px;
    background: rgba(167, 139, 250, 0.5);
    border-radius: 50%;
    animation: float-particle 20s infinite linear;
}

@keyframes float-particle {
    0% {
        transform: translateY(100vh) translateX(0);
        opacity: 0;
    }

    10% {
        opacity: 1;
    }

    90% {
        opacity: 1;
    }

    100% {
        transform: translateY(-100vh) translateX(100px);
        opacity: 0;
    }
}

/* 导航栏 */
.navbar {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--glass);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: 50px;
    padding: 10px 30px;
    z-index: 1000;
    transition: all 0.3s ease;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.nav-container {
    display: flex;
    align-items: center;
    gap: 40px;
}

.logo {
    display: flex;
    align-items: center;
    font-size: 20px;
    font-weight: 800;
    color: white;
    text-decoration: none;
}

.logo-icon {
    width: 32px;
    height: 32px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 10px;
    font-size: 16px;
}

.logo-icon img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: inline-block;
}

.back-btn {
    color: rgba(255, 255, 255, 0.9);
    text-decoration: none;
    font-weight: 500;
    padding: 8px 16px;
    border-radius: 20px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.back-btn:hover {
    background: var(--glass);
    transform: translateX(-3px);
}

/* 主容器 */
.booking-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 120px 20px 60px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 80px;
    align-items: start;
    min-height: 100vh;
}

/* 左侧信息区 */
.info-section {
    position: sticky;
    top: 120px;
}

.info-badge {
    display: inline-block;
    background: var(--glass);
    backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    padding: 8px 20px;
    border-radius: 50px;
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 30px;
    animation: slideInLeft 0.8s ease;
}

.info-title {
    font-size: clamp(36px, 5vw, 56px);
    font-weight: 900;
    line-height: 1.1;
    margin-bottom: 30px;
    background: linear-gradient(135deg,
            #ffffff 0%,
            #a78bfa 25%,
            #60a5fa 50%,
            #ec4899 75%,
            #ffffff 100%);
    background-size: 200% 200%;
    animation: gradient-shift 8s ease infinite, slideInLeft 0.8s ease 0.2s both;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    filter: drop-shadow(0 0 30px rgba(167, 139, 250, 0.5));
}

@keyframes gradient-shift {
    0% {
        background-position: 0% 50%;
    }

    50% {
        background-position: 100% 50%;
    }

    100% {
        background-position: 0% 50%;
    }
}

.info-desc {
    font-size: 20px;
    color: rgba(255, 255, 255, 0.7);
    line-height: 1.6;
    margin-bottom: 30px;
    animation: slideInLeft 0.8s ease 0.4s both;
}

.benefits-list {
    list-style: none;
    margin-bottom: 30px;
    animation: slideInLeft 0.8s ease 0.6s both;
}

.benefit-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 10px 0;
    color: rgba(255, 255, 255, 0.9);
    font-size: 18px;
}

.benefit-icon {
    width: 30px;
    height: 30px;
    background: linear-gradient(135deg, #a78bfa, #60a5fa);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    flex-shrink: 0;
}

.trust-badges {
    display: flex;
    gap: 30px;
    margin-top: 20px;
    animation: slideInLeft 0.8s ease 0.8s both;
}

.trust-item {
    text-align: center;
}

.trust-number {
    font-size: 36px;
    font-weight: 900;
    background: linear-gradient(135deg, #a78bfa 0%, #60a5fa 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    display: block;
}

.trust-label {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.6);
    margin-top: 5px;
}

/* 右侧表单区 */
.form-section {
    background: rgba(255, 255, 255, 0.02);
    border: 1px solid rgba(167, 139, 250, 0.3);
    border-radius: 30px;
    padding: 50px;
    backdrop-filter: blur(20px);
    box-shadow:
        0 25px 50px rgba(0, 0, 0, 0.5),
        inset 0 0 0 1px rgba(255, 255, 255, 0.1);
    position: relative;
    overflow: hidden;
    animation: slideInRight 0.8s ease 0.2s both;
}

.form-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #a78bfa, #60a5fa, #ec4899);
    animation: scan 3s linear infinite;
}

@keyframes scan {
    0% {
        transform: translateX(-100%);
    }

    100% {
        transform: translateX(100%);
    }
}

.form-header {
    text-align: center;
    margin-bottom: 40px;
}

.form-title {
    font-size: 28px;
    font-weight: 500;
    margin-bottom: 10px;
    color: white;
}

.form-subtitle {
    color: rgba(255, 255, 255, 0.6);
    font-size: 16px;
}

/* 表单元素 */
.form-group {
    margin-bottom: 25px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.form-label {
    display: block;
    margin-bottom: 8px;
    color: rgba(255, 255, 255, 0.8);
    font-weight: 500;
    font-size: 14px;
}

.form-label span {
    color: #ef4444;
}

.form-input,
.form-select,
.form-textarea {
    width: 100%;
    padding: 15px 20px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(167, 139, 250, 0.3);
    border-radius: 15px;
    color: white;
    font-size: 14px;
    transition: all 0.3s ease;
    font-family: inherit;
}

.form-input::placeholder,
.form-textarea::placeholder {
    color: rgba(255, 255, 255, 0.4);
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
    outline: none;
    background: rgba(255, 255, 255, 0.08);
    border-color: #a78bfa;
    box-shadow: 0 0 20px rgba(167, 139, 250, 0.3);
}

.form-select {
    cursor: pointer;
}

.form-select option {
    background: #1a0033;
    color: white;
}

.form-textarea {
    min-height: 120px;
    resize: vertical;
}

/* 单选按钮组 */
.radio-group {
    display: flex;
    gap: 20px;
    margin-top: 10px;
}

.radio-label {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    color: rgba(255, 255, 255, 0.8);
    transition: color 0.3s ease;
	font-size: 14px;
}

.radio-input {
    width: 20px;
    height: 20px;
    accent-color: #a78bfa;
}

.radio-label:hover {
    color: white;
}

/* 复选框 */
.checkbox-group {
    display: flex;
    align-items: start;
    gap: 10px;
    margin-top: 20px;
}

.checkbox-input {
    width: 20px;
    height: 20px;
    margin-top: 2px;
    accent-color: #a78bfa;
    cursor: pointer;
}

.checkbox-label {
    color: rgba(255, 255, 255, 0.7);
    font-size: 14px;
    line-height: 1.5;
}

.checkbox-label a {
    color: #a78bfa;
    text-decoration: none;
}

.checkbox-label a:hover {
    text-decoration: underline;
}

/* 提交按钮 */
.submit-btn {
    width: 100%;
    padding: 18px;
    background: linear-gradient(135deg, #a78bfa 0%, #60a5fa 100%);
    border: none;
    border-radius: 25px;
    color: white;
    font-size: 18px;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 30px;
    position: relative;
    overflow: hidden;
}

.submit-btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
}

.submit-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 20px 40px rgba(167, 139, 250, 0.4);
}

.submit-btn:hover::before {
    width: 300px;
    height: 300px;
}

.submit-btn:active {
    transform: translateY(-1px);
}

/* 成功提示 */
.success-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 2000;
}

.success-content {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
    border: 1px solid rgba(167, 139, 250, 0.5);
    border-radius: 30px;
    padding: 50px;
    text-align: center;
    max-width: 500px;
    backdrop-filter: blur(20px);
    animation: scaleIn 0.5s ease;
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.8);
    }

    to {
        opacity: 1;
        transform: scale(1);
    }
}

.success-icon {
    font-size: 80px;
    margin-bottom: 20px;
    animation: bounce 0.5s ease 0.3s both;
}

@keyframes bounce {

    0%,
    100% {
        transform: translateY(0);
    }

    40% {
        transform: translateY(-20px);
    }

    60% {
        transform: translateY(-10px);
    }
}

.success-title {
    font-size: 32px;
    font-weight: 800;
    margin-bottom: 15px;
    background: linear-gradient(135deg, #10b981 0%, #06b6d4 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.success-desc {
    color: rgba(255, 255, 255, 0.8);
    font-size: 18px;
    margin-bottom: 30px;
    line-height: 1.6;
}

.success-btn {
    background: linear-gradient(135deg, #10b981 0%, #06b6d4 100%);
    padding: 15px 40px;
    border: none;
    border-radius: 25px;
    color: white;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.success-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(16, 185, 129, 0.4);
}

/* 动画 */
@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }

    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }

    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* 浮动装饰 */
.floating-shapes {
    position: fixed;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: -1;
}

.shape {
    position: absolute;
    opacity: 0.1;
}

.shape-1 {
    top: 10%;
    right: 10%;
    width: 300px;
    height: 300px;
    background: radial-gradient(circle, #a78bfa 0%, transparent 70%);
    border-radius: 50%;
    animation: float 20s ease-in-out infinite;
}

.shape-2 {
    bottom: 10%;
    left: 5%;
    width: 400px;
    height: 400px;
    background: radial-gradient(circle, #60a5fa 0%, transparent 70%);
    border-radius: 50%;
    animation: float 25s ease-in-out infinite reverse;
}

@keyframes float {

    0%,
    100% {
        transform: translate(0, 0) scale(1);
    }

    33% {
        transform: translate(30px, -30px) scale(1.1);
    }

    66% {
        transform: translate(-20px, 20px) scale(0.9);
    }
}

/* 响应式 */
@media (max-width: 1024px) {
    .booking-container {
        grid-template-columns: 1fr;
        gap: 60px;
    }

    .info-section {
        position: relative;
        top: 0;
        text-align: center;
    }

    .trust-badges {
        justify-content: center;
    }
}

@media (max-width: 768px) {
    .booking-container {
        padding: 100px 20px 40px;
    }

    .form-section {
        padding: 30px 20px;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .info-title {
        font-size: 32px;
    }

    .radio-group {
        flex-direction: column;
    }
}