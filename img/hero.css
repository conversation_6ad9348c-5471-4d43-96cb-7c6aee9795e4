 /* Hero Section */
 .hero {
     min-height: 100vh;
     height: auto;
     position: relative;
     display: flex;
     align-items: center;
     padding: 120px 5% 80px;
     background:
         radial-gradient(ellipse at 20% 80%, rgba(167, 139, 250, 0.3) 0%, transparent 40%),
         radial-gradient(ellipse at 80% 20%, rgba(236, 72, 153, 0.2) 0%, transparent 40%),
         radial-gradient(ellipse at 50% 50%, rgba(96, 165, 250, 0.2) 0%, transparent 60%),
         linear-gradient(180deg, #000000 0%, #0a0014 50%, #1a0033 100%);
 }

 /* 粒子背景 */
 .particles {
     position: absolute;
     top: 0;
     left: 0;
     width: 100%;
     height: 100%;
     overflow: hidden;
     z-index: 1;
 }

 .particle {
     position: absolute;
     width: 2px;
     height: 2px;
     background: rgba(167, 139, 250, 0.5);
     border-radius: 50%;
     animation: float-particle 20s infinite linear;
 }

 @keyframes float-particle {
     0% {
         transform: translateY(100vh) translateX(0);
         opacity: 0;
     }

     10% {
         opacity: 1;
     }

     90% {
         opacity: 1;
     }

     100% {
         transform: translateY(-100vh) translateX(100px);
         opacity: 0;
     }
 }

 /* 网格背景 */
 .grid-bg {
     position: absolute;
     top: 0;
     left: 0;
     width: 100%;
     height: 90vh;
     background-image:
         linear-gradient(rgba(167, 139, 250, 0.1) 1px, transparent 1px),
         linear-gradient(90deg, rgba(167, 139, 250, 0.1) 1px, transparent 1px);
     background-size: 50px 50px;
     transform: perspective(500px) rotateX(60deg) scale(1);
     transform-origin: center center;
     opacity: 0.4;
     z-index: 1;
     /* background: #10b981; */
     /* animation: grid-move 20s linear infinite; */
 }

 @keyframes grid-move {
     0% {
         transform: perspective(500px) rotateX(60deg) scale(1) translateY(0);
     }

     100% {
         transform: perspective(500px) rotateX(60deg) scale(1) translateY(50px);
     }
 }

 .hero-container {
     max-width: 1400px;
     margin: 0 auto;
     display: grid;
     grid-template-columns: 1fr 1fr;
     gap: 80px;
     align-items: center;
     position: relative;
     z-index: 10;
 }

 /* 标题动效 */
 .hero-content h1 {
     font-size: clamp(48px, 7vw, 80px);
     font-weight: 800;
     line-height: 1.1;
     margin-bottom: 30px;
	 padding-top: 20px;
     position: relative;
 }

 .gradient-text {
     background: linear-gradient(135deg,
             #ffffff 0%,
             #a78bfa 25%,
             #60a5fa 50%,
             #ec4899 75%,
             #ffffff 100%);
     background-size: 200% 200%;
     animation: gradient-shift 8s ease infinite;
     -webkit-background-clip: text;
     -webkit-text-fill-color: transparent;
     filter: drop-shadow(0 0 30px rgba(167, 139, 250, 0.5));
 }

 @keyframes gradient-shift {
     0% {
         background-position: 0% 50%;
     }

     50% {
         background-position: 100% 50%;
     }

     100% {
         background-position: 0% 50%;
     }
 }

 /* 光线装饰 */
 .light-decoration {
     position: absolute;
     top: 0px;
     left: 0;
     width: 100%;
     height: 2px;
     background: linear-gradient(90deg, transparent, #a78bfa, #60a5fa, transparent);
     animation: light-move 3s ease-in-out infinite;
 }

 @keyframes light-move {

     0%,
     100% {
         transform: translateX(-100%);
     }

     50% {
         transform: translateX(100%);
     }
 }

 /* 副标题打字机效果 */
 .hero-subtitle {
     font-size: 20px;
     color: rgba(255, 255, 255, 0.7);
     line-height: 1.6;
     margin-bottom: 40px;
     min-height: 60px;
 }

 .typing-text {
     display: inline-block;
     position: relative;
 }

 .typing-text::after {
     content: '|';
     position: absolute;
     right: -10px;
     animation: blink 1s infinite;
 }

 @keyframes blink {

     0%,
     50% {
         opacity: 1;
     }

     51%,
     100% {
         opacity: 0;
     }
 }

 /* 按钮组 */
 .hero-buttons {
     display: flex;
     gap: 20px;
     margin-bottom: 40px;
 }

 .btn {
     padding: 16px 40px;
     border-radius: 30px;
     text-decoration: none;
     font-size: 18px;
     font-weight: 500;
     transition: all 0.3s ease;
     position: relative;
     overflow: hidden;
     display: inline-block;
 }

 .btn-primary {
     background: linear-gradient(135deg, #a78bfa 0%, #60a5fa 100%);
     color: white;
     box-shadow: 0 4px 15px rgba(167, 139, 250, 0.3);
 }

 .btn-primary:hover {
     transform: translateY(-3px);
     box-shadow: 0 10px 30px rgba(167, 139, 250, 0.5);
 }

 .btn-secondary {
     background: rgba(255, 255, 255, 0.05);
     color: white;
     border: 1px solid rgba(255, 255, 255, 0.2);
     backdrop-filter: blur(10px);
 }

 .btn-secondary:hover {
     background: rgba(255, 255, 255, 0.1);
     border-color: rgba(167, 139, 250, 0.5);
     box-shadow: 0 0 20px rgba(167, 139, 250, 0.3);
 }

 /* 磁性效果 */
 .magnetic {
     transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
 }

 /* 数据展示 */
 .stats {
     display: flex;
     gap: 40px;
 }

 .stat-item {
     position: relative;
 }

 .stat-number {
     font-size: 48px;
     font-weight: 700;
     background: linear-gradient(135deg, #a78bfa 0%, #60a5fa 100%);
     -webkit-background-clip: text;
     -webkit-text-fill-color: transparent;
     margin-bottom: 5px;
 }

 .stat-label {
     font-size: 14px;
     color: rgba(255, 255, 255, 0.6);
     text-transform: uppercase;
     letter-spacing: 1px;
 }

 /* 3D产品展示 */
 .hero-visual {
     position: relative;
     perspective: 1000px;
 }

 .hero-showcase {
     position: relative;
     transform-style: preserve-3d;
     animation: float 6s ease-in-out infinite;
 }

 @keyframes float {

     0%,
     100% {
         transform: translateY(0) rotateY(-10deg) rotateX(5deg);
     }

     50% {
         transform: translateY(-20px) rotateY(-10deg) rotateX(5deg);
     }
 }

 /* 主界面卡片 */
 .main-dashboard {
     width: 100%;
     height: 500px;
     background: rgba(255, 255, 255, 0.02);
     border: 1px solid rgba(167, 139, 250, 0.3);
     border-radius: 20px;
     overflow: hidden;
     position: relative;
     backdrop-filter: blur(10px);
     box-shadow:
         0 25px 50px rgba(0, 0, 0, 0.5),
         inset 0 0 0 1px rgba(255, 255, 255, 0.1);
     transform: rotateY(-10deg) rotateX(5deg);
 }

 /* 全息效果 */
 .holographic-effect {
     position: absolute;
     top: 0;
     left: 0;
     width: 100%;
     height: 100%;
     background: linear-gradient(45deg,
             transparent 30%,
             rgba(167, 139, 250, 0.1) 50%,
             transparent 70%);
     background-size: 200% 200%;
     animation: holographic-sweep 3s ease-in-out infinite;
     pointer-events: none;
 }

 @keyframes holographic-sweep {
     0% {
         background-position: -100% 0;
     }

     100% {
         background-position: 100% 0;
     }
 }

 .dashboard-header {
     background: rgba(255, 255, 255, 0.03);
     padding: 20px 30px;
     display: flex;
     justify-content: space-between;
     align-items: center;
     border-bottom: 1px solid rgba(167, 139, 250, 0.2);
 }

 .dashboard-title {
     font-size: 16px;
     color: rgba(255, 255, 255, 0.9);
     font-weight: 500;
 }

 .dashboard-status {
     display: flex;
     gap: 8px;
 }

 .status-dot {
     width: 8px;
     height: 8px;
     border-radius: 50%;
     background: #10b981;
     box-shadow: 0 0 10px #10b981;
     animation: pulse 2s infinite;
 }

 @keyframes pulse {

     0%,
     100% {
         opacity: 1;
         transform: scale(1);
     }

     50% {
         opacity: 0.5;
         transform: scale(1.2);
     }
 }

 /* 数据可视化 */
 .dashboard-content {
     padding: 30px;
     display: grid;
     grid-template-columns: repeat(3, 1fr);
     gap: 20px;
 }

 .data-card {
     background: rgba(255, 255, 255, 0.02);
     border: 1px solid rgba(167, 139, 250, 0.2);
     padding: 25px;
     border-radius: 15px;
     position: relative;
     overflow: hidden;
 }

 .data-card::before {
     content: '';
     position: absolute;
     top: 0;
     left: 0;
     width: 100%;
     height: 3px;
     background: linear-gradient(90deg, #a78bfa, #60a5fa);
     animation: scan 3s linear infinite;
 }

 @keyframes scan {
     0% {
         transform: translateX(-100%);
     }

     100% {
         transform: translateX(100%);
     }
 }

 .card-icon {
     width: 40px;
     height: 40px;
     background: linear-gradient(135deg, #a78bfa, #60a5fa);
     border-radius: 10px;
     display: flex;
     align-items: center;
     justify-content: center;
     margin-bottom: 15px;
     font-size: 20px;
 }

 .card-value {
     font-size: 32px;
     font-weight: 600;
     color: #a78bfa;
     margin-bottom: 5px;
 }

 .card-label {
     font-size: 14px;
     color: rgba(255, 255, 255, 0.6);
 }

 /* 浮动元素 */
 .floating-elements {
     position: absolute;
     top: 0;
     left: 0;
     width: 100%;
     height: 100%;
     pointer-events: none;
     z-index: 5;
 }

 .floating-card {
     position: absolute;
     background: rgba(255, 255, 255, 0.03);
     border: 1px solid rgba(167, 139, 250, 0.3);
     padding: 15px 20px;
     border-radius: 10px;
     backdrop-filter: blur(10px);
     animation: float-card 10s ease-in-out infinite;
 }

 .floating-card:nth-child(1) {
     top: 35%;
     right: -60px;
     animation-delay: 0s;
 }

 .floating-card:nth-child(2) {
     top: 80%;
     left: 580px;
     animation-delay: 3s;
 }

 @keyframes float-card {

     0%,
     100% {
         transform: translateY(0) translateX(0) rotateZ(0deg);
     }

     33% {
         transform: translateY(-20px) translateX(-10px) rotateZ(2deg);
     }

     66% {
         transform: translateY(10px) translateX(5px) rotateZ(-1deg);
     }
 }

 .floating-text {
     font-size: 14px;
     color: rgba(255, 255, 255, 0.8);
     margin-bottom: 5px;
 }

 .floating-value {
     font-size: 18px;
     font-weight: 600;
     color: #60a5fa;
 }



 /* 响应式设计 */
 @media (max-width: 1024px) {
     .hero-container {
         grid-template-columns: 1fr;
         gap: 40px;
     }

     .hero-visual {
         display: none;
     }
 }

 @media (max-width: 768px) {
     .nav-menu {
         display: none;
     }

     .hero-content h1 {
         font-size: 36px;
     }

     .hero-buttons {
         flex-direction: column;
     }

     .stats {
         flex-direction: column;
         gap: 20px;
     }
 }