:root {
 --primary: #6366f1;
 --primary-dark: #4338ca;
 --secondary: #06b6d4;
 --accent: #f59e0b;
 --success: #10b981;
 --danger: #ef4444;
 --dark: #0f172a;
 --light: #f8fafc;
 --text-primary: #1e293b;
 --text-secondary: #64748b;
 --gradient-primary: linear-gradient(135deg, #6366f1 0%, #8b5cf6 50%, #06b6d4 100%);
 --gradient-bg: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
 --glass: rgba(255, 255, 255, 0.1);
 --glass-border: rgba(255, 255, 255, 0.2);
}
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}
body {
    font-family: 'Inter', 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    overflow-x: hidden;
    scroll-behavior: smooth;
}
/* 全屏加载动画 */
.loader {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 1;
    transition: opacity 0.5s ease;
}
.loader.hidden {
    opacity: 0;
    pointer-events: none;
}
.loader-content {
    text-align: center;
    color: white;
}
.loader-logo {
    font-size: 4rem;
    margin-bottom: 20px;
    animation: pulse 1.5s ease-in-out infinite;
}
.loader-text {
    font-size: 1.5rem;
    font-weight: 600;
}
 @keyframes pulse {
 0%, 100% {
 transform: scale(1);
}
 50% {
 transform: scale(1.1);
}
}
/* 超现代导航栏 */
.navbar {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--glass);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: 50px;
    padding: 10px 30px;
    z-index: 1000;
    transition: all 0.3s ease;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}
.navbar.scrolled {
    top: 10px;
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(0, 0, 0, 0.1);
}
.nav-container {
    display: flex;
    align-items: center;
    gap: 40px;
}
.logo {
    display: flex;
    align-items: center;
    font-size: 20px;
    font-weight: 800;
    color: white;
    text-decoration: none;
}
.navbar.scrolled .logo {
    color: var(--primary);
}
.logo-icon {
    width: 32px;
    height: 32px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 10px;
    font-size: 16px;
}
.logo-icon img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: inline-block;
}
.nav-links {
    display: flex;
    list-style: none;
    gap: 30px;
}
.nav-links a {
    color: rgba(255, 255, 255, 0.9);
    text-decoration: none;
    font-weight: 500;
    padding: 8px 16px;
    border-radius: 20px;
    transition: all 0.3s ease;
}
.navbar.scrolled .nav-links a {
    color: var(--text-secondary);
}
.nav-links a:hover {
    background: var(--glass);
    color: white;
    transform: translateY(-2px);
}
.cta-button {
    background: var(--gradient-primary);
    color: white;
    padding: 12px 24px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 20px rgba(99, 102, 241, 0.4);
}
.cta-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 30px rgba(99, 102, 241, 0.6);
}
.btn-hero-primary {
    background: white;
    color: var(--primary);
    padding: 18px 36px;
    border-radius: 50px;
    text-decoration: none;
    font-weight: 700;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    box-shadow: 0 10px 40px rgba(255, 255, 255, 0.3);
}
.btn-hero-secondary {
    background: transparent;
    color: white;
    padding: 18px 36px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50px;
    text-decoration: none;
    font-weight: 700;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}
.btn-hero-primary:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 60px rgba(255, 255, 255, 0.4);
}
.btn-hero-secondary:hover {
    background: var(--glass);
    border-color: white;
    transform: translateY(-5px);
}
/* 价值主张区域 */
.value-section {
    padding: 120px 0;
    background: linear-gradient(180deg, var(--light) 0%, white 100%);
}
.value-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    text-align: center;
}
.section-badge {
    display: inline-block;
    background: var(--gradient-primary);
    color: white;
    padding: 8px 24px;
    border-radius: 50px;
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 20px;
}
.section-title {
    font-size: 3.5rem;
    font-weight: 900;
    color: var(--text-primary);
    margin-bottom: 20px;
    line-height: 1.2;
}
.section-subtitle {
    font-size: 1.3rem;
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto 80px;
    line-height: 1.6;
}
.value-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 40px;
    margin-bottom: 80px;
}
.value-card {
    background: white;
    border-radius: 30px;
    padding: 50px 30px;
    text-align: center;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.05);
    border: 1px solid #f1f5f9;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}
.value-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}
.value-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 40px 100px rgba(0, 0, 0, 0.1);
}
.value-card:hover::before {
    transform: scaleX(1);
}
.value-icon {
    width: 80px;
    height: 80px;
    background: var(--gradient-primary);
    border-radius: 25px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 25px;
    font-size: 2rem;
    position: relative;
}
.value-icon::after {
    content: '';
    position: absolute;
    top: -10px;
    left: -10px;
    right: -10px;
    bottom: -10px;
    background: var(--gradient-primary);
    border-radius: 35px;
    opacity: 0.1;
    z-index: -1;
}
.value-card h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 15px;
}
.value-card p {
    color: var(--text-secondary);
    line-height: 1.6;
    font-size: 1rem;
}
/* 问题-解决方案区域 */
.problem-solution {
    padding: 120px 0;
    background: var(--text-primary);
    color: white;
    position: relative;
    overflow: hidden;
}
.problem-solution::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 30% 40%, rgba(99, 102, 241, 0.2) 0%, transparent 50%), radial-gradient(circle at 70% 60%, rgba(139, 92, 246, 0.2) 0%, transparent 50%);
}
.problem-solution-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    position: relative;
    z-index: 2;
}
.before-after {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 80px;
    align-items: center;
}
.problem-side {
    text-align: left;
}
.problem-title {
    font-size: 2.5rem;
    font-weight: 800;
    margin-bottom: 30px;
    color: #ef4444;
}
.problem-list {
    list-style: none;
    margin-bottom: 40px;
}
.problem-list li {
    padding: 15px 0;
    font-size: 1.1rem;
    position: relative;
    padding-left: 35px;
    opacity: 0.9;
    background: url('../img/duicuo2.png') no-repeat left center;
    background-size: 20px 20px;
}
.solution-side {
    text-align: left;
}
.solution-title {
    font-size: 2.5rem;
    font-weight: 800;
    margin-bottom: 30px;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}
.solution-list {
    list-style: none;
    margin-bottom: 40px;
}
.solution-list li {
    padding: 15px 0;
    font-size: 1.1rem;
    position: relative;
    padding-left: 35px;
    opacity: 0.9;
    background: url('../img/duicuo1.png') no-repeat left center;
    background-size: 20px 20px;
}
/* 产品展示区域 */
.product-showcase {
    padding: 120px 0;
}
.showcase-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
}
.showcase-header {
    text-align: center;
    margin-bottom: 80px;
}
.feature-tabs {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-bottom: 60px;
    flex-wrap: wrap;
}
.tab-button {
    background: white;
    border: 2px solid #e2e8f0;
    color: var(--text-secondary);
    padding: 15px 30px;
    border-radius: 50px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}
.tab-button.active {
    background: var(--gradient-primary);
    border-color: transparent;
    color: white;
    transform: translateY(-3px);
    box-shadow: 0 10px 30px rgba(99, 102, 241, 0.3);
}
.feature-content {
    display: none;
    animation: fadeInUp 0.5s ease;
}
.feature-content.active {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 80px;
    align-items: center;
}
.feature-info h3 {
    font-size: 2.5rem;
    font-weight: 800;
    color: var(--text-primary);
    margin-bottom: 20px;
}
.feature-info p {
    font-size: 1.2rem;
    color: var(--text-secondary);
    margin-bottom: 30px;
    line-height: 1.6;
}
.feature-benefits {
    list-style: none;
    margin-bottom: 40px;
}
.feature-benefits li {
    padding: 10px 0;
    position: relative;
    padding-left: 30px;
    font-size: 1.1rem;
    color: var(--text-secondary);
    background: url('../img/iconfont-tag.png') no-repeat left center;
    background-size: 20px 20px;
}
/* 右侧可视化区域 - 重新设计 */
.feature-visual-bg {
    background: linear-gradient(180deg, #1a1a2e 0%, #0f1123 100%);
    border-radius: 30px;
}
.feature-visual {
    height: 400px;
    position: relative;
    border-radius: 30px;
    overflow: hidden;
}
/* Tab 1: 智能制度管理 */
.visual-1 {
    background: linear-gradient(135deg, rgba(99, 102, 241, 0.1) 0%, rgba(6, 182, 212, 0.1) 100%);
    border: 1px solid rgba(99, 102, 241, 0.3);
    padding: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}
.doc-flow {
    display: flex;
    align-items: center;
    gap: 30px;
    position: relative;
}
.doc-node {
    width: 120px;
    height: 120px;
    background: rgba(255, 255, 255, 0.05);
    border: 2px solid rgba(99, 102, 241, 0.5);
    border-radius: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    animation: float 6s ease-in-out infinite;
}
.doc-node:nth-child(2) {
    animation-delay: 2s;
}
.doc-node:nth-child(4) {
    animation-delay: 4s;
}
 @keyframes float {
 0%, 100% {
transform: translateY(0);
}
 50% {
transform: translateY(-20px);
}
}
.doc-icon {
    font-size: 40px;
    margin-bottom: 8px;
}
.doc-text {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.8);
}
.flow-arrow {
    font-size: 24px;
    color: #6366f1;
    animation: pulse 2s ease-in-out infinite;
}
 @keyframes pulse {
 0%, 100% {
	opacity: 1;
	transform: scale(1);
	}
	 50% {
	opacity: 0.8;
	transform: scale(1.2);
	}
}
/* Tab 2: 风险智能识别 */
.visual-2 {
    background: radial-gradient(circle at center, rgba(99, 102, 241, 0.2) 0%, transparent 60%);
    border: 1px solid rgba(99, 102, 241, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
}
.radar-system {
    width: 350px;
    height: 350px;
    position: relative;
}
.radar-circle {
    position: absolute;
    border: 1px solid rgba(99, 102, 241, 0.2);
    border-radius: 50%;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
}
.radar-circle:nth-child(1) {
    width: 100px;
    height: 100px;
}
.radar-circle:nth-child(2) {
    width: 200px;
    height: 200px;
}
.radar-circle:nth-child(3) {
    width: 300px;
    height: 300px;
}
.radar-sweep {
    position: absolute;
    width: 100%;
    height: 100%;
    background: conic-gradient(from 0deg, transparent 0deg, rgba(99, 102, 241, 0.4) 30deg, transparent 60deg);
    border-radius: 50%;
    animation: radar-rotate 4s linear infinite;
}
 @keyframes radar-rotate {
	 0% {
	transform: rotate(0deg);
	}
	 100% {
	transform: rotate(360deg);
	}
}
.risk-dot {
    position: absolute;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    animation: blink 2s ease-in-out infinite;
}
.risk-high {
    background: #ef4444;
    box-shadow: 0 0 20px #ef4444;
}
.risk-medium {
    background: #f59e0b;
    box-shadow: 0 0 20px #f59e0b;
}
.risk-low {
    background: #10b981;
    box-shadow: 0 0 20px #10b981;
}
 @keyframes blink {
 0%, 100% {
	opacity: 1;
	}
	 50% {
	opacity: 0.3;
	}
}
.risk-stats {
    position: absolute;
    bottom: 10px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 30px;
    background: rgba(0, 0, 0, 0.1);
    padding: 10px 30px;
    border-radius: 15px;
    backdrop-filter: blur(10px);
}
.risk-stat {
    text-align: center;
}
.risk-value {
    font-size: 28px;
    font-weight: 700;
    display: block;
}
.risk-label {
    font-size: 12px;
    opacity: 0.7;
    color: #ffffff;
}
/* Tab 3: 实时监控预警 */
.visual-3 {
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(99, 102, 241, 0.1) 100%);
    border: 1px solid rgba(16, 185, 129, 0.3);
    padding: 30px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}
/*新*/
.monitor-card {
    background: rgba(255, 255, 255, 0.02);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 15px;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}
.monitor-card:hover {
    transform: translateY(-5px);
    border-color: rgba(99, 102, 241, 0.5);
}
.monitor-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}
.monitor-title {
    font-size: 16px;
    color: rgba(255, 255, 255, 0.9);
}
.monitor-status {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #10b981;
    box-shadow: 0 0 10px #10b981;
    animation: pulse-dot 2s infinite;
}
 @keyframes pulse-dot {
 0%, 100% {
	opacity: 1;
	transform: scale(1);
	}
	 50% {
	opacity: 0.5;
	transform: scale(1.5);
	}
}
.monitor-chart {
    height: 60px;
    background: linear-gradient(180deg, rgba(99, 102, 241, 0.2) 0%, transparent 100%);
    border-radius: 10px;
    margin-bottom: 0px;
    position: relative;
    overflow: hidden;
}
.chart-line {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 60%;
    background: linear-gradient(90deg, #6366f1, #06b6d4);
    clip-path: polygon( 0% 100%, 10% 80%, 20% 85%, 30% 70%, 40% 75%, 50% 60%, 60% 65%, 70% 50%, 80% 55%, 90% 40%, 100% 45%, 100% 100% );
    animation: chart-wave 3s ease-in-out infinite;
}
 @keyframes chart-wave {
 0%, 100% {
	transform: translateX(0);
	}
	 50% {
	transform: translateX(-10px);
	}
}
.monitor-value {
    font-size: 28px;
    font-weight: 700;
    background: linear-gradient(135deg, #6366f1, #06b6d4);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
	display: inline-block;
}
.monitor-label {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.6);
	display: inline-block;
}
/* Tab 4: AI智能审查 */
.visual-4 {
    background: rgba(139, 92, 246, 0.1);
    border: 1px solid rgba(139, 92, 246, 0.3);
    padding: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}
.ai-assistant {
    text-align: center;
}
.ai-avatar {
    width: 120px;
    height: 120px;
    background: linear-gradient(135deg, #8b5cf6, #6366f1);
    border-radius: 50%;
    margin: 0 auto 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 60px;
    animation: rotate 20s linear infinite;
    box-shadow: 0 20px 40px rgba(139, 92, 246, 0.3);
}
 @keyframes rotate {
	 0% {
	transform: rotate(0deg);
	}
	 100% {
	transform: rotate(360deg);
	}
}
.ai-status {
    font-size: 18px;
    color: #10b981;
    margin-bottom: 20px;
}
.ai-stats {
    display: flex;
    gap: 40px;
    justify-content: center;
    margin-top: 40px;
}
.ai-stat {
    text-align: center;
}
.ai-stat-value {
    font-size: 32px;
    font-weight: 700;
    color: #8b5cf6;
    display: block;
}
.ai-stat-label {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.6);
}
 @keyframes fadeInUp {
	 from {
	 opacity: 0;
	 transform: translateY(30px);
	}
	 to {
	 opacity: 1;
	 transform: translateY(0);
	}
}
/* 客户成功区域 */
.success-section {
    padding: 120px 0;
    background: var(--text-primary);
    color: white;
}
.success-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    text-align: center;
}
.success-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 40px;
    margin-top: 80px;
}
.success-card {
    background: var(--glass);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: 30px;
    padding: 30px;
    transition: all 0.3s ease;
}
.success-card:hover {
    transform: translateY(-10px);
    background: rgba(255, 255, 255, 0.15);
}
.success-quote {
    font-size: 1.2rem;
    line-height: 1.6;
    margin-bottom: 30px;
    font-style: italic;
    opacity: 0.9;
}
.success-author {
    display: flex;
    align-items: center;
    gap: 15px;
}
.author-avatar {
    width: 50px;
    height: 50px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1.2rem;
}
.author-info h4 {
    font-weight: 600;
    margin-bottom: 5px;
}
.author-info p {
    opacity: 0.7;
    font-size: 0.9rem;
}
.success-metrics {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 10px;
    margin: 40px 0;
}
.metric {
    text-align: center;
    padding: 10px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
}
.metric-number {
    font-size: 1.4rem;
    font-weight: 900;
    color: var(--accent);
    display: block;
}
.metric-label {
    font-size: 0.9rem;
    opacity: 0.8;
    margin-top: 5px;
}
/* 超级CTA区域 */
.super-cta {
    padding: 120px 0;
    background: var(--gradient-primary);
    text-align: center;
    color: white;
    position: relative;
    overflow: hidden;
}
.super-cta::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 20% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%), radial-gradient(circle at 80% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
    animation: pulse-bg 4s ease-in-out infinite;
}
 @keyframes pulse-bg {
 0%, 100% {
 opacity: 1;
}
 50% {
 opacity: 0.8;
}
}
.super-cta-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 0 20px;
    position: relative;
    z-index: 2;
}
.super-cta h2 {
    font-size: 4rem;
    font-weight: 900;
    margin-bottom: 30px;
    line-height: 1.1;
}
.super-cta p {
    font-size: 1.4rem;
    margin-bottom: 50px;
    opacity: 0.9;
    line-height: 1.6;
}
.super-cta-buttons {
    display: flex;
    justify-content: center;
    gap: 30px;
    margin-bottom: 50px;
    flex-wrap: wrap;
}
.btn-super {
    padding: 20px 40px;
    border-radius: 50px;
    font-size: 1.2rem;
    font-weight: 700;
    text-decoration: none;
    transition: all 0.3s ease;
    min-width: 200px;
}
.btn-super-primary {
    background: white;
    color: var(--primary);
    box-shadow: 0 15px 50px rgba(255, 255, 255, 0.3);
}
.btn-super-secondary {
    background: transparent;
    color: white;
    border: 3px solid white;
}
.btn-super:hover {
    transform: translateY(-5px);
    box-shadow: 0 25px 70px rgba(255, 255, 255, 0.4);
}
.contact-info {
    display: flex;
    justify-content: center;
    gap: 40px;
    flex-wrap: wrap;
    opacity: 0.8;
}
.contact-item {
    display: flex;
    gap: 10px;
    font-size: 1.1rem;
    align-items: center;
}
.contact-item img {
    width: 24px;
    height: 24px;
    display: inline;
    overflow: hidden;
}
/* 现代化页脚 */
.footer {
    background: #020617;
    color: white;
    padding: 80px 0 40px;
}
.footer-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}
.footer-content {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr;
    gap: 60px;
    margin-bottom: 60px;
}
.footer-brand {
    max-width: 350px;
}
.footer-logo {
    display: flex;
    align-items: center;
    font-size: 24px;
    font-weight: 800;
    margin-bottom: 20px;
    color: white;
}
.footer-logo-icon {
    width: 40px;
    height: 40px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    font-size: 18px;
}
.footer-logo-icon img {
    width: 90%;
 height: logo-icon%;
    display: inline-block;
}
.footer-desc {
    font-size: 1rem;
    line-height: 1.6;
    opacity: 0.8;
    margin-bottom: 30px;
}
.footer-section h3 {
    font-size: 1.2rem;
    font-weight: 700;
    margin-bottom: 20px;
    color: var(--accent);
}
.footer-links {
    list-style: none;
}
.footer-links li {
    margin-bottom: 12px;
}
.footer-links a {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    transition: color 0.3s ease;
}
.footer-links a:hover {
    color: var(--accent);
}
.foot-link-ico {
    display: flex;
    align-items: center;
    gap: 8px;
}
.foot-link-ico img {
    width: 20px;
    height: 20px;
    display: inline;
}
.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 40px;
    text-align: center;
}
.footer-bottom p {
    opacity: 0.6;
    margin-bottom: 10px;
}
 @keyframes float {
 0%, 100% {
 transform: translateY(0px);
}
 50% {
 transform: translateY(-10px);
}
}

/* 响应式设计 */
@media (max-width: 1024px) {
.before-after {
    grid-template-columns: 1fr;
    gap: 60px;
}
.feature-content.active {
    grid-template-columns: 1fr;
    gap: 40px;
}
.footer-content {
    grid-template-columns: 1fr 1fr;
    gap: 40px;
}
}

@media (max-width: 768px) {
.nav-links {
    display: none;
}
/* .hero-title {
                font-size: 2.8rem;
            } */

.section-title {
    font-size: 2.5rem;
}
.super-cta h2 {
    font-size: 2.8rem;
}

.super-cta-buttons {
    flex-direction: column;
    align-items: center;
}
.contact-info {
    flex-direction: column;
    align-items: center;
}
.footer-content {
    grid-template-columns: 1fr;
    text-align: center;
}
}
/* 特殊效果 */
.parallax-element {
    position: absolute;
    opacity: 0.1;
    animation: parallax 20s linear infinite;
}
 @keyframes parallax {
 0% {
 transform: translateY(100vh) rotate(0deg);
}
 100% {
 transform: translateY(-100vh) rotate(360deg);
}
}
.glow-effect {
    position: relative;
}
.glow-effect::after {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    /* background: var(--gradient-primary); */
    border-radius: inherit;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
}
.glow-effect:hover::after {
    opacity: 0.7;
    animation: glow 1.5s ease-in-out infinite alternate;
}
 @keyframes glow {
 from {
 box-shadow: 0 0 20px var(--primary);
}
 to {
 box-shadow: 0 0 40px var(--primary), 0 0 60px var(--primary);
}
}
