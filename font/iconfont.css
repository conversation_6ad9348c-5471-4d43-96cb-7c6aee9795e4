@font-face {
  font-family: "iconfont"; /* Project id 4951664 */
  src: url('iconfont.woff2?t=1752312929787') format('woff2'),
       url('iconfont.woff?t=1752312929787') format('woff'),
       url('iconfont.ttf?t=1752312929787') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-iconfonttag:before {
  content: "\e620";
}

.icon-jiankong:before {
  content: "\e623";
}

.icon-jiqiren:before {
  content: "\e6ac";
}

.icon-guanlian:before {
  content: "\e610";
}

.icon-mind-full:before {
  content: "\e850";
}

.icon-mind2-full:before {
  content: "\e851";
}

.icon-brain-full:before {
  content: "\e910";
}

.icon-brain:before {
  content: "\e911";
}

.icon-jianba:before {
  content: "\e672";
}

.icon-robot-2-line:before {
  content: "\e7a3";
}

.icon-zhengshulist:before {
  content: "\e6d5";
}

.icon-zhengshu:before {
  content: "\e606";
}

.icon-tousujianyi:before {
  content: "\e64f";
}

.icon-robot-line:before {
  content: "\e7a2";
}

.icon-AI:before {
  content: "\ec5f";
}

.icon-tousu:before {
  content: "\e668";
}

